"""
PRÉDICTEUR INDEX5 - DÉTERMINATION DES VALEURS POSSIBLES À LA MAIN N+1
=====================================================================

Programme qui détermine les 6 valeurs possibles d'INDEX5 à la main n+1
basé sur les règles de transition d'INDEX1 selon INDEX2.
Les possibilités TIE à l'INDEX3 sont exclues.

RÈGLES DE TRANSITION INDEX1 :
- Si INDEX1 = 0 et INDEX2 = C à la main n → INDEX1 = 1 à la main n+1
- Si INDEX1 = 1 et INDEX2 = C à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 0 et INDEX2 = A à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 1 et INDEX2 = A à la main n → INDEX1 = 1 à la main n+1
- Si INDEX1 = 0 et INDEX2 = B à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 1 et INDEX2 = B à la main n → INDEX1 = 1 à la main n+1

INDEX5 = INDEX1_INDEX2_INDEX3 (avec INDEX3 ∈ {BANKER, PLAYER})
"""

using JSON
using Printf
using Dates

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURES ET TYPES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    MainData

Structure pour stocker les données d'une main.
"""
struct MainData
    main_number::Union{Int, Nothing}
    manche_pb_number::Union{Int, Nothing}
    index1::Union{Int, String}
    index2::String
    index3::String
    index5::String
end

"""
    FormulasTheoretical{T<:AbstractFloat}

Structure contenant les probabilités théoriques INDEX5 et paramètres pour les calculs d'entropie.
"""
mutable struct FormulasTheoretical{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}
    sequence_complete::Vector{String}  # Séquence complète pour calculs empiriques

    function FormulasTheoretical{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        # Probabilités théoriques INDEX5 (identiques à entropie_baccarat_analyzer.jl)
        theoretical_probs = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
            "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
            "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
            "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
            "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
            "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
            "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
        )
        new{T}(base, epsilon, theoretical_probs, String[])
    end
end

# Constructeur de convenance
FormulasTheoretical(base::Real = 2.0, epsilon::Real = 1e-12) =
    FormulasTheoretical{Float64}(Float64(base), Float64(epsilon))

"""
    MetriquesTheorique{T<:AbstractFloat}

Structure pour stocker les 10 métriques théoriques calculées.
ORDRE DE PRIORITÉ PRÉDICTIVE (du plus important au moins important) :
1. InfoMutT - Information Mutuelle (dépendance directe)
2. CondT - Entropie Conditionnelle (prévisibilité immédiate)
3. DivKLT - Divergence KL (biais du modèle)
4. CrossT - Entropie Croisée (inefficacité du modèle)
5. MetricT - Entropie Métrique (variation de complexité)
6. TopoT - Entropie Topologique (patterns multi-échelles)
7. TauxT - Taux d'Entropie (complexité normalisée)
8. ShannonT - Entropie de Shannon (diversité observée)
9. BlockT - Entropie Jointe (complexité totale)
"""
struct MetriquesTheorique{T<:AbstractFloat}
    cond_t::T         # 1. CondT - PRIORITÉ 1
    divkl_t::T        # 2. DivKLT - PRIORITÉ 2
    cross_t::T        # 3. CrossT - PRIORITÉ 2
    metric_t::T       # 4. MetricT - PRIORITÉ 3
    topo_t::T         # 5. TopoT - PRIORITÉ 3
    taux_t::T         # 6. TauxT - PRIORITÉ 4
    shannon_t::T      # 7. ShannonT - PRIORITÉ 5
    block_t::T        # 8. BlockT - PRIORITÉ 5
end

"""
    DifferentielsPredictifs{T<:AbstractFloat}

Structure pour stocker les différentiels des 10 métriques théoriques pour la prédiction.
Calcule |métrique(n+1) - métrique(n)| pour chacune des 6 possibilités à la main n+1.
ORDRE DE PRIORITÉ PRÉDICTIVE (du plus important au moins important) :
"""
struct DifferentielsPredictifs{T<:AbstractFloat}
    # Différentiels des 8 métriques théoriques (ordre de priorité prédictive)
    diff_cond_t::T         # 1. |CondT(n+1) - CondT(n)| - PRIORITÉ 1
    diff_divkl_t::T        # 2. |DivKLT(n+1) - DivKLT(n)| - PRIORITÉ 2
    diff_cross_t::T        # 3. |CrossT(n+1) - CrossT(n)| - PRIORITÉ 2
    diff_metric_t::T       # 4. |MetricT(n+1) - MetricT(n)| - PRIORITÉ 3
    diff_topo_t::T         # 5. |TopoT(n+1) - TopoT(n)| - PRIORITÉ 3
    diff_taux_t::T         # 6. |TauxT(n+1) - TauxT(n)| - PRIORITÉ 4
    diff_shannon_t::T      # 7. |ShannonT(n+1) - ShannonT(n)| - PRIORITÉ 5
    diff_block_t::T        # 8. |BlockT(n+1) - BlockT(n)| - PRIORITÉ 5
end

"""
    CalculateurDifferentielsPredictifs{T<:AbstractFloat}

Classe pour calculer les différentiels prédictifs des métriques théoriques.
"""
struct CalculateurDifferentielsPredictifs{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function CalculateurDifferentielsPredictifs{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

# Constructeur de convenance
CalculateurDifferentielsPredictifs(base::Real = 2.0, epsilon::Real = 1e-12) =
    CalculateurDifferentielsPredictifs{Float64}(Float64(base), Float64(epsilon))

"""
    PredictionResult

Structure pour stocker le résultat de prédiction pour une main avec métriques et différentiels.
"""
struct PredictionResult
    main_actuelle::Int
    index5_actuel::String
    index1_suivant::Int
    index5_observe::Union{String, Nothing}  # INDEX5 réellement observé à la main n+1
    valeurs_possibles::Vector{String}
    metriques_par_possibilite::Vector{MetriquesTheorique{Float64}}
    differentiels_par_possibilite::Vector{DifferentielsPredictifs{Float64}}
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    trouver_fichier_json_recent(dossier::String) -> String

Trouve le fichier JSON le plus récent dans le dossier spécifié.
"""
function trouver_fichier_json_recent(dossier::String)
    if !isdir(dossier)
        throw(ArgumentError("Le dossier '$dossier' n'existe pas"))
    end
    
    fichiers_json = filter(f -> endswith(f, ".json"), readdir(dossier))
    
    if isempty(fichiers_json)
        throw(ArgumentError("Aucun fichier JSON trouvé dans le dossier '$dossier'"))
    end
    
    # Trier par date de modification (plus récent en premier)
    fichiers_avec_dates = [(f, stat(joinpath(dossier, f)).mtime) for f in fichiers_json]
    sort!(fichiers_avec_dates, by=x->x[2], rev=true)
    
    fichier_recent = fichiers_avec_dates[1][1]
    chemin_complet = joinpath(dossier, fichier_recent)
    
    println("📁 Fichier JSON le plus récent trouvé : $fichier_recent")
    return chemin_complet
end

"""
    charger_donnees_partie(chemin_fichier::String, numero_partie::Int) -> Vector{MainData}

Charge les données d'une partie spécifique depuis un fichier JSON.
"""
function charger_donnees_partie(chemin_fichier::String, numero_partie::Int)
    println("📖 Chargement du fichier : $chemin_fichier")
    println("🎯 Recherche de la partie numéro : $numero_partie")

    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)

        if !haskey(donnees, "parties_condensees") || isempty(donnees["parties_condensees"])
            throw(ArgumentError("Format de fichier invalide : 'parties_condensees' manquant ou vide"))
        end

        # Chercher la partie demandée
        partie_trouvee = nothing
        for partie in donnees["parties_condensees"]
            if partie["partie_number"] == numero_partie
                partie_trouvee = partie
                break
            end
        end

        if partie_trouvee === nothing
            throw(ArgumentError("Partie numéro $numero_partie non trouvée"))
        end

        mains_data = partie_trouvee["mains_condensees"]

        # Convertir en structures MainData
        mains = MainData[]

        for main_data in mains_data
            # Ignorer les mains avec des données manquantes
            if main_data["main_number"] === nothing ||
               main_data["index1"] === "" ||
               main_data["index2"] === "" ||
               main_data["index3"] === ""
                continue
            end

            push!(mains, MainData(
                main_data["main_number"],
                main_data["manche_pb_number"],
                main_data["index1"],
                main_data["index2"],
                main_data["index3"],
                main_data["index5"]
            ))
        end

        println("✅ Partie $numero_partie chargée : $(length(mains)) mains valides")
        println("📊 Statistiques de la partie :")
        if haskey(partie_trouvee, "statistiques")
            stats = partie_trouvee["statistiques"]
            println("   • Total mains : $(stats["total_mains"])")
            println("   • Manches P/B : $(stats["total_manches_pb"])")
            println("   • Ties : $(stats["total_ties"])")
        end

        return mains

    catch e
        println("❌ Erreur lors du chargement : $e")
        rethrow(e)
    end
end

"""
    compter_parties_disponibles(chemin_fichier::String) -> Int

Compte le nombre de parties disponibles dans le fichier JSON.
"""
function compter_parties_disponibles(chemin_fichier::String)
    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)

        if !haskey(donnees, "parties_condensees")
            return 0
        end

        return length(donnees["parties_condensees"])

    catch e
        println("❌ Erreur lors du comptage des parties : $e")
        return 0
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# CLASSES DÉDIÉES POUR CHAQUE MÉTRIQUE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    MetriqueCondT{T<:AbstractFloat}

Classe dédiée pour le calcul de l'Entropie Conditionnelle (CondT) - PRIORITÉ 1.
Mesure la prévisibilité immédiate du système INDEX5.
"""
struct MetriqueCondT{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function MetriqueCondT{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

MetriqueCondT(base::Real = 2.0, epsilon::Real = 1e-12) = MetriqueCondT{Float64}(Float64(base), Float64(epsilon))

"""
    calculer_formule5B_conditionnelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 5B : Entropie Conditionnelle Cumulative (VERSION THÉORIQUE CORRIGÉE)
Entropie conditionnelle cumulative moyenne de toute la séquence [1:n] selon la Chain Rule.
Formule : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
Relation : CondT_n = H_theo(X₁,...,Xₙ) / n
Signification : Prévisibilité globale du système INDEX5
"""
function calculer_formule5B_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            h_i = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # Calculer la somme des entropies conditionnelles : ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_entropies_conditionnelles = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_entropies_conditionnelles += h_cond
    end

    # Retourner la moyenne : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    return somme_entropies_conditionnelles / T(n)
end

"""
    calculer_formule1B_shannon_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 1B : Entropie de Shannon Jointe (VERSION THÉORIQUE)
Formule : H_theo(X₁, X₂, ..., Xₙ) = -∑ p_theo(x₁,...,xₙ) log₂ p_theo(x₁,...,xₙ)

Calcule l'entropie jointe théorique pour la fenêtre croissante de la main 1 à la main n.
Utilise les probabilités théoriques INDEX5 du modèle.
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # CORRECTION : Calculer l'entropie de Shannon de la DISTRIBUTION théorique
    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)
    total = length(subsequence)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon : -∑ p_theo(x) log₂ p_theo(x)
            # Pondérée par la fréquence d'apparition dans la séquence
            weight = T(count) / T(total)
            entropy -= weight * p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end


"""
    MetriqueDivKLT{T<:AbstractFloat}

Classe dédiée pour le calcul de la Divergence KL (DivKLT) - PRIORITÉ 2.
Mesure le biais du modèle INDEX5.
"""
struct MetriqueDivKLT{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function MetriqueDivKLT{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

MetriqueDivKLT(base::Real = 2.0, epsilon::Real = 1e-12) = MetriqueDivKLT{Float64}(Float64(base), Float64(epsilon))

"""
    MetriqueCrossT{T<:AbstractFloat}

Classe dédiée pour le calcul de l'Entropie Croisée (CrossT) - PRIORITÉ 2.
Mesure l'inefficacité du modèle INDEX5.
"""
struct MetriqueCrossT{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function MetriqueCrossT{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

MetriqueCrossT(base::Real = 2.0, epsilon::Real = 1e-12) = MetriqueCrossT{Float64}(Float64(base), Float64(epsilon))

"""
    MetriqueMetricT{T<:AbstractFloat}

Classe dédiée pour le calcul de l'Entropie Métrique (MetricT) - PRIORITÉ 3.
Mesure la variation de complexité du système INDEX5.
"""
struct MetriqueMetricT{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function MetriqueMetricT{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

MetriqueMetricT(base::Real = 2.0, epsilon::Real = 1e-12) = MetriqueMetricT{Float64}(Float64(base), Float64(epsilon))


"""
    MetriqueTopoT{T<:AbstractFloat}

Classe dédiée pour le calcul de l'Entropie Topologique (TopoT) - PRIORITÉ 3.
Mesure les patterns multi-échelles du système INDEX5.
"""
struct MetriqueTopoT{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function MetriqueTopoT{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

MetriqueTopoT(base::Real = 2.0, epsilon::Real = 1e-12) = MetriqueTopoT{Float64}(Float64(base), Float64(epsilon))

"""
    MetriqueTauxT{T<:AbstractFloat}

Classe dédiée pour le calcul du Taux d'Entropie (TauxT) - PRIORITÉ 4.
Mesure la complexité normalisée du système INDEX5.
"""
struct MetriqueTauxT{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function MetriqueTauxT{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

MetriqueTauxT(base::Real = 2.0, epsilon::Real = 1e-12) = MetriqueTauxT{Float64}(Float64(base), Float64(epsilon))



"""
    calculer_formule10B_block_cumulative_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 10B : Entropie de Block Cumulative (VERSION THÉORIQUE)
Formule : H_n_theo = H_theo(X₁, X₂, ..., Xₙ)

Identique à l'entropie jointe de Shannon théorique

"""
function calculer_formule10B_block_cumulative_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    # Cette formule est identique à la formule 1B (Shannon Jointe théorique)
    return calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)
end



"""
    MetriqueShannonT{T<:AbstractFloat}

Classe dédiée pour le calcul de l'Entropie de Shannon (ShannonT) - PRIORITÉ 5.
Mesure la diversité observée du système INDEX5.
"""
struct MetriqueShannonT{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function MetriqueShannonT{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

MetriqueShannonT(base::Real = 2.0, epsilon::Real = 1e-12) = MetriqueShannonT{Float64}(Float64(base), Float64(epsilon))

"""
    MetriqueBlockT{T<:AbstractFloat}

Classe dédiée pour le calcul de l'Entropie de Bloc (BlockT) - PRIORITÉ 5.
Mesure la complexité totale jointe du système INDEX5.
"""
struct MetriqueBlockT{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function MetriqueBlockT{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

MetriqueBlockT(base::Real = 2.0, epsilon::Real = 1e-12) = MetriqueBlockT{Float64}(Float64(base), Float64(epsilon))

"""
    calculer_probabilite_conditionnelle_theo(formulas::FormulasTheoretical{T}, current_value::String, context::Vector{String}) where T -> T

Calcule la vraie probabilité conditionnelle théorique p_theo(xᵢ|x₁,...,xᵢ₋₁)
Sans hypothèse d'indépendance - utilise les probabilités jointes théoriques.
Formule : p(xᵢ|x₁,...,xᵢ₋₁) = p(x₁,...,xᵢ) / p(x₁,...,xᵢ₋₁)
"""
function calculer_probabilite_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    current_value::String,
    context::Vector{String}
) where T<:AbstractFloat

    # Construire la séquence complète : context + current_value
    full_sequence = vcat(context, [current_value])

    # Calculer p_theo(x₁,...,xᵢ) - probabilité jointe de la séquence complète
    p_joint_full = calculer_probabilite_jointe_theo(formulas, full_sequence)

    # Calculer p_theo(x₁,...,xᵢ₋₁) - probabilité jointe du contexte
    p_joint_context = calculer_probabilite_jointe_theo(formulas, context)

    # p_theo(xᵢ|x₁,...,xᵢ₋₁) = p_theo(x₁,...,xᵢ) / p_theo(x₁,...,xᵢ₋₁)
    if p_joint_context > zero(T)
        return p_joint_full / p_joint_context
    else
        # Si le contexte a une probabilité nulle, utiliser la probabilité marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end
"""

"""
    calculer_probabilite_conditionnelle_empirique(formulas::FormulasTheoretical{T}, current_value::String, previous_value::String) where T -> T

Calcule la probabilité conditionnelle empirique p_empirique(xᵢ|xᵢ₋₁) basée sur les observations
dans la séquence complète disponible dans formulas.
Formule : p_empirique(xᵢ|xᵢ₋₁) = count(xᵢ₋₁ → xᵢ) / count(xᵢ₋₁)
"""
function calculer_probabilite_conditionnelle_empirique(
    formulas::FormulasTheoretical{T},
    current_value::String,
    previous_value::String
) where T<:AbstractFloat

    # Accéder à la séquence complète stockée dans formulas
    # Si la séquence complète n'est pas disponible, utiliser une approximation
    if !hasfield(typeof(formulas), :sequence_complete) || isempty(formulas.sequence_complete)
        # Fallback : utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end

    sequence_complete = formulas.sequence_complete

    # Compter les transitions previous_value → current_value
    count_transition = 0
    count_previous = 0

    for i in 1:(length(sequence_complete) - 1)
        if sequence_complete[i] == previous_value
            count_previous += 1
            if sequence_complete[i + 1] == current_value
                count_transition += 1
            end
        end
    end

    # Calculer p_empirique(current|previous) = count(previous → current) / count(previous)
    if count_previous > 0
        return T(count_transition) / T(count_previous)
    else
        # Si previous_value n'a jamais été observé, utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

"""

"""
    calculer_probabilite_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}) where T -> T

Calcule la probabilité jointe théorique d'une séquence en utilisant les probabilités conditionnelles
empiriques basées sur les observations de la séquence complète.
Pour une séquence de longueur 1, retourne p_theo(x₁).
Pour une séquence plus longue, utilise la règle de chaîne avec les probabilités conditionnelles empiriques.
"""
function calculer_probabilite_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String}
) where T<:AbstractFloat

    if isempty(sequence)
        return zero(T)
    end

    if length(sequence) == 1
        # p_theo(x₁) - utiliser la probabilité théorique
        return get(formulas.theoretical_probs, sequence[1], formulas.epsilon)
    end

    # Pour les séquences plus longues, utiliser les probabilités conditionnelles empiriques
    # basées sur les observations dans la séquence complète disponible

    joint_prob = one(T)

    # Premier terme : p_theo(x₁) - probabilité théorique du premier élément
    first_value = sequence[1]
    p_first = get(formulas.theoretical_probs, first_value, formulas.epsilon)
    joint_prob *= p_first

    # Termes suivants : p_empirique(xᵢ|xᵢ₋₁) basé sur les observations
    for i in 2:length(sequence)
        current_value = sequence[i]
        previous_value = sequence[i-1]

        # Calculer p_empirique(xᵢ|xᵢ₋₁) basé sur les observations dans la séquence complète
        p_conditional_empirique = calculer_probabilite_conditionnelle_empirique(
            formulas, current_value, previous_value
        )

        joint_prob *= p_conditional_empirique
    end

    return joint_prob
end

"""
    GestionnaireMetriques{T<:AbstractFloat}

Gestionnaire centralisé pour toutes les 8 métriques théoriques.
Permet de calculer toutes les métriques de manière organisée et efficace.
"""
struct GestionnaireMetriques{T<:AbstractFloat}
    metrique_cond::MetriqueCondT{T}      # 1. PRIORITÉ 1
    metrique_divkl::MetriqueDivKLT{T}    # 2. PRIORITÉ 2
    metrique_cross::MetriqueCrossT{T}    # 3. PRIORITÉ 2
    metrique_metric::MetriqueMetricT{T}  # 4. PRIORITÉ 3
    metrique_topo::MetriqueTopoT{T}      # 5. PRIORITÉ 3
    metrique_taux::MetriqueTauxT{T}      # 6. PRIORITÉ 4
    metrique_shannon::MetriqueShannonT{T} # 7. PRIORITÉ 5
    metrique_block::MetriqueBlockT{T}    # 8. PRIORITÉ 5

    function GestionnaireMetriques{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        new{T}(
            MetriqueCondT{T}(base, epsilon),
            MetriqueDivKLT{T}(base, epsilon),
            MetriqueCrossT{T}(base, epsilon),
            MetriqueMetricT{T}(base, epsilon),
            MetriqueTopoT{T}(base, epsilon),
            MetriqueTauxT{T}(base, epsilon),
            MetriqueShannonT{T}(base, epsilon),
            MetriqueBlockT{T}(base, epsilon)
        )
    end
end

GestionnaireMetriques(base::Real = 2.0, epsilon::Real = 1e-12) = GestionnaireMetriques{Float64}(Float64(base), Float64(epsilon))

"""
    calculer_toutes_metriques(gestionnaire::GestionnaireMetriques{T}, sequence::Vector{String}, n::Int) where T -> MetriquesTheorique{T}

Calcule toutes les 8 métriques en utilisant le gestionnaire centralisé.
"""
function calculer_toutes_metriques(gestionnaire::GestionnaireMetriques{T}, sequence::Vector{String}, n::Int) where T<:AbstractFloat
    # Synchroniser les séquences complètes pour tous les calculateurs
    gestionnaire.metrique_cond.formulas.sequence_complete = sequence
    gestionnaire.metrique_divkl.formulas.sequence_complete = sequence
    gestionnaire.metrique_cross.formulas.sequence_complete = sequence
    gestionnaire.metrique_metric.formulas.sequence_complete = sequence
    gestionnaire.metrique_topo.formulas.sequence_complete = sequence
    gestionnaire.metrique_taux.formulas.sequence_complete = sequence
    gestionnaire.metrique_shannon.formulas.sequence_complete = sequence
    gestionnaire.metrique_block.formulas.sequence_complete = sequence

    # Calcul des 8 métriques dans l'ordre de priorité prédictive
    cond_t = calculer(gestionnaire.metrique_cond, sequence, n)      # 1. PRIORITÉ 1
    divkl_t = calculer(gestionnaire.metrique_divkl, sequence, n)    # 2. PRIORITÉ 2
    cross_t = calculer(gestionnaire.metrique_cross, sequence, n)    # 3. PRIORITÉ 2
    metric_t = calculer(gestionnaire.metrique_metric, sequence, n)  # 4. PRIORITÉ 3
    topo_t = calculer(gestionnaire.metrique_topo, sequence, n)      # 5. PRIORITÉ 3
    taux_t = calculer(gestionnaire.metrique_taux, sequence, n)      # 6. PRIORITÉ 4
    shannon_t = calculer(gestionnaire.metrique_shannon, sequence, n) # 7. PRIORITÉ 5
    block_t = calculer(gestionnaire.metrique_block, sequence, n)    # 8. PRIORITÉ 5

    return MetriquesTheorique{T}(
        cond_t, divkl_t, cross_t, metric_t,
        topo_t, taux_t, shannon_t, block_t
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# MÉTHODES DE CALCUL POUR CHAQUE CLASSE DE MÉTRIQUE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer(metrique::MetriqueCondT{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'Entropie Conditionnelle Cumulative (CondT) - PRIORITÉ 1.
FORMULE 5B : Entropie Conditionnelle Cumulative (VERSION THÉORIQUE CORRIGÉE)
Entropie conditionnelle cumulative moyenne de toute la séquence [1:n] selon la Chain Rule.
Formule : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
Relation : CondT_n = H_theo(X₁,...,Xₙ) / n
Signification : Prévisibilité globale du système INDEX5
"""
function calculer(metrique::MetriqueCondT{T}, sequence::Vector{String}, n::Int) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(metrique.formulas.theoretical_probs, value, metrique.formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(metrique.formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            # Appel direct aux fonctions Shannon définies dans ce fichier
            h_i = calculer_formule1B_shannon_jointe_theo(metrique.formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(metrique.formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # Calculer la somme des entropies conditionnelles : ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_entropies_conditionnelles = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_entropies_conditionnelles += h_cond
    end

    # Retourner la moyenne : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    return somme_entropies_conditionnelles / T(n)
end

"""
    calculer(metrique::MetriqueDivKLT{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule la Divergence KL (DivKLT) - PRIORITÉ 2.
FORMULE 6B : Divergence KL Observée vs Théorique (VERSION CORRIGÉE)
Mesure l'écart entre les fréquences réellement observées et les probabilités théoriques INDEX5.
Formule : DivKLT = ∑ p_obs(x) × log₂(p_obs(x)/p_theo(x))
Usage standard : p_obs = distribution "vraie" (observée), p_theo = distribution "approximative" (modèle)
"""
function calculer(metrique::MetriqueDivKLT{T}, sequence::Vector{String}, n::Int) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer la divergence KL : ∑ p_obs(x) × log₂(p_obs(x)/p_theo(x))
    divergence = zero(T)
    n_total = T(length(subsequence))

    for (value, count) in counts
        # Calculer p_obs(x) = count(x dans séquence [1:n]) / n
        p_obs = T(count) / n_total

        # Récupérer p_theo(x) depuis le modèle INDEX5
        p_theo = get(metrique.formulas.theoretical_probs, value, metrique.formulas.epsilon)

        if p_obs > zero(T) && p_theo > zero(T)
            # Divergence KL standard : p_obs × log₂(p_obs/p_theo)
            divergence += p_obs * (log(p_obs / p_theo) / log(metrique.formulas.base))
        end
    end

    return divergence
end

"""
    calculer(metrique::MetriqueCrossT{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'Entropie Croisée (CrossT) - PRIORITÉ 2.
FORMULE 8B : Entropie Croisée Observée vs Théorique (VERSION CORRIGÉE)
Mesure le coût d'encodage des données réellement observées en utilisant les probabilités théoriques INDEX5 comme modèle de codage.
Formule : CrossT = -∑ p_obs(x) × log₂ p_theo(x)
Usage standard : p_obs = distribution "vraie" (observée), p_theo = distribution "de codage" (modèle)
"""
function calculer(metrique::MetriqueCrossT{T}, sequence::Vector{String}, n::Int) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie croisée : -∑ p_obs(x) × log₂ p_theo(x)
    cross_entropy = zero(T)
    n_total = T(length(subsequence))

    for (value, count) in counts
        # Calculer p_obs(x) = count(x dans séquence [1:n]) / n
        p_obs = T(count) / n_total

        # Récupérer p_theo(x) depuis le modèle INDEX5
        p_theo = get(metrique.formulas.theoretical_probs, value, metrique.formulas.epsilon)

        if p_obs > zero(T) && p_theo > zero(T)
            # Entropie croisée standard : -p_obs × log₂(p_theo)
            cross_entropy -= p_obs * (log(p_theo) / log(metrique.formulas.base))
        end
    end

    return cross_entropy
end

"""
    calculer(metrique::MetriqueMetricT{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'Entropie Métrique (MetricT) - PRIORITÉ 3.
FORMULE 4B : Entropie Métrique Kolmogorov-Sinai (VERSION THÉORIQUE CORRIGÉE)
Entropie métrique pondérée : mesure l'impact de l'ajout du n-ème élément sur la complexité informationnelle pondérée globale.
Formule : MetricT_n = [(2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)] - [(2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)]
"""
function calculer(metrique::MetriqueMetricT{T}, sequence::Vector{String}, n::Int) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(metrique.formulas.theoretical_probs, value, metrique.formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(metrique.formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            # Appel direct aux fonctions Shannon définies dans ce fichier
            h_i = calculer_formule1B_shannon_jointe_theo(metrique.formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(metrique.formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # ÉTAPE 1 - Calcul pour la séquence [1:n-1]
    # Complexité_pondérée(n-1) = (2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n_minus_1 = zero(T)
    for i in 1:(n-1)
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n_minus_1 += T(i) * h_cond
    end
    complexite_n_minus_1 = (T(2) / (T(n-1) * T(n))) * somme_ponderee_n_minus_1

    # ÉTAPE 2 - Calcul pour la séquence [1:n]
    # Complexité_pondérée(n) = (2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n += T(i) * h_cond
    end
    complexite_n = (T(2) / (T(n) * T(n+1))) * somme_ponderee_n

    # ÉTAPE 3 - DIFFÉRENCE (ENTROPIE MÉTRIQUE PONDÉRÉE)
    # MetricT_n = Complexité_pondérée(n) - Complexité_pondérée(n-1)
    return complexite_n - complexite_n_minus_1
end

"""
    calculer(metrique::MetriqueTopoT{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'Entropie Topologique (TopoT) - PRIORITÉ 3.
FORMULE 9B : Entropie Topologique Multi-Échelles (VERSION THÉORIQUE CORRIGÉE)
Entropie topologique multi-échelles basée sur la complexité théorique INDEX5 à 3 niveaux de résolution.
Formule : TopoT_n = 0.167 × H_theo(blocs_1) + 0.333 × H_theo(blocs_2) + 0.500 × H_theo(blocs_3)
Pondération basée sur la capacité informationnelle théorique de chaque échelle.
"""
function calculer(metrique::MetriqueTopoT{T}, sequence::Vector{String}, n::Int) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Fonction auxiliaire pour calculer H_theo(blocs_k) avec probabilités théoriques INDEX5
    function calculer_entropie_blocs(block_size::Int)
        if block_size > length(subsequence)
            return zero(T)
        end

        # Extraire tous les blocs de taille block_size
        blocs_distincts = Set{Vector{String}}()
        for i in 1:(length(subsequence) - block_size + 1)
            bloc = subsequence[i:i+block_size-1]
            push!(blocs_distincts, bloc)
        end

        # Calculer l'entropie théorique des blocs distincts
        entropy = zero(T)
        for bloc in blocs_distincts
            # Calculer la probabilité théorique du bloc sous hypothèse d'indépendance
            p_bloc_theo = one(T)
            for value in bloc
                p_value_theo = get(metrique.formulas.theoretical_probs, value, metrique.formulas.epsilon)
                p_bloc_theo *= p_value_theo
            end

            if p_bloc_theo > zero(T)
                entropy -= p_bloc_theo * (log(p_bloc_theo) / log(metrique.formulas.base))
            end
        end

        return entropy
    end

    # Calculer les entropies pour chaque échelle
    h_blocs_1 = calculer_entropie_blocs(1)  # Valeurs individuelles
    h_blocs_2 = n >= 2 ? calculer_entropie_blocs(2) : zero(T)  # Paires consécutives
    h_blocs_3 = n >= 3 ? calculer_entropie_blocs(3) : zero(T)  # Triplets consécutifs

    # Poids théoriques basés sur la capacité informationnelle
    w1 = T(0.167)  # 16.7% pour complexité locale
    w2 = T(0.333)  # 33.3% pour complexité des transitions
    w3 = T(0.500)  # 50.0% pour complexité des motifs

    # Entropie topologique multi-échelles pondérée
    topo_entropy = w1 * h_blocs_1 + w2 * h_blocs_2 + w3 * h_blocs_3

    return topo_entropy
end

"""
    calculer(metrique::MetriqueTauxT{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule le Taux d'Entropie (TauxT) - PRIORITÉ 4.
FORMULE 3B : Taux d'Entropie (VERSION OPTIMISÉE)
Calcule le taux d'entropie théorique en utilisant l'entropie de bloc.
Formule : TauxT_n = (1/n) × BlockT_n = (1/n) × ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)
Relation directe avec BlockT pour éviter la duplication de code.
"""
function calculer(metrique::MetriqueTauxT{T}, sequence::Vector{String}, n::Int) where T<:AbstractFloat
    # Appel direct à la fonction TauxT définie dans ce fichier
    return calculer_formule3B_taux_entropie_theo(metrique.formulas, sequence, n)
end

"""
    calculer(metrique::MetriqueShannonT{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'Entropie de Shannon (ShannonT) - PRIORITÉ 5.
FORMULE 1B : Entropie de Shannon Jointe (VERSION THÉORIQUE)
Calcule l'entropie jointe théorique pour la fenêtre croissante de la main 1 à la main n.
"""
function calculer(metrique::MetriqueShannonT{T}, sequence::Vector{String}, n::Int) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)
    total = length(subsequence)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(metrique.formulas.theoretical_probs, value, metrique.formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon pure : H(X) = -∑ p(x) log₂(p(x))
            # Formule correcte selon cours_entropie/niveau_debutant/02_formule_shannon.md
            # Caractères : H = -∑ pᵢ × log₂(pᵢ)
            entropy -= p_theo * (log(p_theo) / log(metrique.formulas.base))
        end
    end

    return entropy
end

"""
    calculer(metrique::MetriqueBlockT{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'Entropie de Bloc (BlockT) - PRIORITÉ 5.
FORMULE 10B : Entropie de Bloc Empirique Pure (VERSION CORRIGÉE POUR COHÉRENCE)
Calcule la vraie entropie jointe selon la règle de chaîne généralisée avec probabilités empiriques.
Formule : BlockT_n = H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)
Avec : H(X₁) = -log₂(1/18) ≈ 4.170 bits (probabilité empirique uniforme)
Basée sur la règle de chaîne : p(x₁,...,xₙ) = p_emp(x₁) × ∏ᵢ₌₂ⁿ p_emp(xᵢ|x₁,...,xᵢ₋₁)
"""
function calculer(metrique::MetriqueBlockT{T}, sequence::Vector{String}, n::Int) where T<:AbstractFloat
    # Appel direct à la fonction BlockT définie dans ce fichier
    return calculer_formule10B_block_cumulative_theo(metrique.formulas, sequence, n)
end

# ═══════════════════════════════════════════════════════════════════════════════
# CALCULS DES MÉTRIQUES THÉORIQUES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule3B_taux_entropie_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 3B : Taux d'Entropie (VERSION OPTIMISÉE)
Calcule le taux d'entropie théorique en utilisant l'entropie de bloc.
Formule : TauxT_n = (1/n) × BlockT_n = (1/n) × ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)
Relation directe avec BlockT pour éviter la duplication de code.
"""
function calculer_formule3B_taux_entropie_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie de bloc et la diviser par n
    block_entropy = calculer_formule10B_block_cumulative_theo(formulas, sequence, n)

    # Retourner le taux d'entropie : TauxT_n = BlockT_n / n
    return block_entropy / T(n)
end






    calculer_formule4B_entropie_metrique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 4B : Entropie Métrique Kolmogorov-Sinai (VERSION THÉORIQUE CORRIGÉE)
Entropie métrique pondérée : mesure l'impact de l'ajout du n-ème élément sur la complexité informationnelle pondérée globale.
Formule : MetricT_n = [(2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)] - [(2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)]
"""
function calculer_formule4B_entropie_metrique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            h_i = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # ÉTAPE 1 - Calcul pour la séquence [1:n-1]
    # Complexité_pondérée(n-1) = (2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n_minus_1 = zero(T)
    for i in 1:(n-1)
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n_minus_1 += T(i) * h_cond
    end
    complexite_n_minus_1 = (T(2) / (T(n-1) * T(n))) * somme_ponderee_n_minus_1

    # ÉTAPE 2 - Calcul pour la séquence [1:n]
    # Complexité_pondérée(n) = (2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n += T(i) * h_cond
    end
    complexite_n = (T(2) / (T(n) * T(n+1))) * somme_ponderee_n

    # ÉTAPE 3 - DIFFÉRENCE (ENTROPIE MÉTRIQUE PONDÉRÉE)
    # MetricT_n = Complexité_pondérée(n) - Complexité_pondérée(n-1)
    return complexite_n - complexite_n_minus_1
end

"""
    calculer_toutes_metriques_theoriques(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> MetriquesTheorique{T}

Calcule toutes les 8 métriques théoriques pour une séquence donnée jusqu'à la main n.
Utilise les classes dédiées pour chaque métrique.
"""
function calculer_toutes_metriques_theoriques(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat

    # Initialiser la séquence complète pour les calculs de probabilités conditionnelles empiriques
    formulas.sequence_complete = sequence

    # Créer les instances des classes de métriques
    metrique_cond = MetriqueCondT{T}(formulas.base, formulas.epsilon)
    metrique_divkl = MetriqueDivKLT{T}(formulas.base, formulas.epsilon)
    metrique_cross = MetriqueCrossT{T}(formulas.base, formulas.epsilon)
    metrique_metric = MetriqueMetricT{T}(formulas.base, formulas.epsilon)
    metrique_topo = MetriqueTopoT{T}(formulas.base, formulas.epsilon)
    metrique_taux = MetriqueTauxT{T}(formulas.base, formulas.epsilon)
    metrique_shannon = MetriqueShannonT{T}(formulas.base, formulas.epsilon)
    metrique_block = MetriqueBlockT{T}(formulas.base, formulas.epsilon)

    # Synchroniser les séquences complètes
    metrique_cond.formulas.sequence_complete = sequence
    metrique_divkl.formulas.sequence_complete = sequence
    metrique_cross.formulas.sequence_complete = sequence
    metrique_metric.formulas.sequence_complete = sequence
    metrique_topo.formulas.sequence_complete = sequence
    metrique_taux.formulas.sequence_complete = sequence
    metrique_shannon.formulas.sequence_complete = sequence
    metrique_block.formulas.sequence_complete = sequence

    # Calcul des 8 métriques dans l'ordre de priorité prédictive
    cond_t = calculer(metrique_cond, sequence, n)              # 1. PRIORITÉ 1
    divkl_t = calculer(metrique_divkl, sequence, n)            # 2. PRIORITÉ 2
    cross_t = calculer(metrique_cross, sequence, n)            # 3. PRIORITÉ 2
    metric_t = calculer(metrique_metric, sequence, n)          # 4. PRIORITÉ 3
    topo_t = calculer(metrique_topo, sequence, n)              # 5. PRIORITÉ 3
    taux_t = calculer(metrique_taux, sequence, n)              # 6. PRIORITÉ 4
    shannon_t = calculer(metrique_shannon, sequence, n)        # 7. PRIORITÉ 5
    block_t = calculer(metrique_block, sequence, n)            # 8. PRIORITÉ 5

    return MetriquesTheorique{T}(
        cond_t, divkl_t, cross_t, metric_t,
        topo_t, taux_t, shannon_t, block_t
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# CALCUL DES DIFFÉRENTIELS PRÉDICTIFS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_differentiels_predictifs(calculateur::CalculateurDifferentielsPredictifs{T}, metriques_n::MetriquesTheorique{T}, metriques_n_plus_1::MetriquesTheorique{T}) where T -> DifferentielsPredictifs{T}

Calcule les différentiels prédictifs entre les métriques de la main n et n+1.
Formule : |métrique(n+1) - métrique(n)| pour chacune des 10 métriques théoriques.
"""
function calculer_differentiels_predictifs(
    calculateur::CalculateurDifferentielsPredictifs{T},
    metriques_n::MetriquesTheorique{T},
    metriques_n_plus_1::MetriquesTheorique{T}
) where T<:AbstractFloat

    # Calculer les différentiels absolus |métrique(n+1) - métrique(n)| (ordre de priorité prédictive, InfoMutT et CondDecT supprimés)
    diff_cond_t = abs(metriques_n_plus_1.cond_t - metriques_n.cond_t)             # 1. PRIORITÉ 1
    diff_divkl_t = abs(metriques_n_plus_1.divkl_t - metriques_n.divkl_t)          # 2. PRIORITÉ 2
    diff_cross_t = abs(metriques_n_plus_1.cross_t - metriques_n.cross_t)          # 3. PRIORITÉ 2
    diff_metric_t = abs(metriques_n_plus_1.metric_t - metriques_n.metric_t)       # 4. PRIORITÉ 3
    diff_topo_t = abs(metriques_n_plus_1.topo_t - metriques_n.topo_t)             # 5. PRIORITÉ 3
    diff_taux_t = abs(metriques_n_plus_1.taux_t - metriques_n.taux_t)             # 6. PRIORITÉ 4
    diff_shannon_t = abs(metriques_n_plus_1.shannon_t - metriques_n.shannon_t)    # 7. PRIORITÉ 5
    diff_block_t = abs(metriques_n_plus_1.block_t - metriques_n.block_t)          # 8. PRIORITÉ 5

    return DifferentielsPredictifs{T}(
        diff_cond_t, diff_divkl_t, diff_cross_t, diff_metric_t,
        diff_topo_t, diff_taux_t, diff_shannon_t, diff_block_t
    )
end

"""
    calculer_differentiels_pour_possibilites(calculateur::CalculateurDifferentielsPredictifs{T}, sequence_n::Vector{String}, valeurs_possibles::Vector{String}) where T -> Vector{DifferentielsPredictifs{T}}

Calcule les différentiels prédictifs pour toutes les possibilités à la main n+1.
"""
function calculer_differentiels_pour_possibilites(
    calculateur::CalculateurDifferentielsPredictifs{T},
    sequence_n::Vector{String},
    valeurs_possibles::Vector{String}
) where T<:AbstractFloat

    n = length(sequence_n)

    # Calculer les métriques pour la main n (état actuel)
    metriques_n = calculer_toutes_metriques_theoriques(calculateur.formulas, sequence_n, n)

    # Calculer les différentiels pour chaque possibilité
    differentiels = DifferentielsPredictifs{T}[]

    for valeur_possible in valeurs_possibles
        # Créer la séquence hypothétique avec cette valeur ajoutée
        sequence_n_plus_1 = vcat(sequence_n, [valeur_possible])

        # Calculer les métriques pour la main n+1 (état hypothétique)
        metriques_n_plus_1 = calculer_toutes_metriques_theoriques(calculateur.formulas, sequence_n_plus_1, n + 1)

        # Calculer les différentiels |métrique(n+1) - métrique(n)|
        diff = calculer_differentiels_predictifs(calculateur, metriques_n, metriques_n_plus_1)
        push!(differentiels, diff)
    end

    return differentiels
end

# ═══════════════════════════════════════════════════════════════════════════════
# LOGIQUE DE PRÉDICTION
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_index1_suivant(index1_actuel::Int, index2_actuel::String) -> Int

Calcule la valeur d'INDEX1 pour la main suivante selon les règles de transition.
"""
function calculer_index1_suivant(index1_actuel::Int, index2_actuel::String)
    if index2_actuel == "C"
        # Règle C : INDEX1 s'inverse
        return index1_actuel == 0 ? 1 : 0
    elseif index2_actuel == "A" || index2_actuel == "B"
        # Règles A et B : INDEX1 reste identique
        return index1_actuel
    else
        throw(ArgumentError("INDEX2 invalide : '$index2_actuel'. Doit être A, B ou C"))
    end
end

"""
    generer_valeurs_possibles(index1_suivant::Int) -> Vector{String}

Génère les 6 valeurs possibles d'INDEX5 pour la main suivante.
Exclut les possibilités TIE à l'INDEX3.
Ordre : d'abord tous les BANKER (A, B, C), puis tous les PLAYER (A, B, C).
"""
function generer_valeurs_possibles(index1_suivant::Int)
    index2_possibles = ["A", "B", "C"]
    index3_possibles = ["BANKER", "PLAYER"]  # TIE supprimé

    valeurs_possibles = String[]

    # D'abord tous les BANKER : A, B, C
    for index2 in index2_possibles
        index5 = "$(index1_suivant)_$(index2)_BANKER"
        push!(valeurs_possibles, index5)
    end

    # Puis tous les PLAYER : A, B, C
    for index2 in index2_possibles
        index5 = "$(index1_suivant)_$(index2)_PLAYER"
        push!(valeurs_possibles, index5)
    end

    return valeurs_possibles
end

"""
    predire_main_suivante(main_actuelle::MainData, sequence_jusqu_n::Vector{String}, index5_observe::Union{String, Nothing} = nothing) -> PredictionResult

Prédit les valeurs possibles pour la main suivante avec calcul des métriques théoriques.
"""
function predire_main_suivante(main_actuelle::MainData, sequence_jusqu_n::Vector{String}, index5_observe::Union{String, Nothing} = nothing)
    # Calculer INDEX1 pour la main suivante
    index1_suivant = calculer_index1_suivant(main_actuelle.index1, main_actuelle.index2)

    # Générer toutes les valeurs possibles
    valeurs_possibles = generer_valeurs_possibles(index1_suivant)

    # Initialiser les structures pour les calculs d'entropie et différentiels
    formulas = FormulasTheoretical{Float64}()
    calculateur_diff = CalculateurDifferentielsPredictifs{Float64}()

    # Calculer les métriques pour la main n (état actuel)
    n = length(sequence_jusqu_n)
    metriques_n = calculer_toutes_metriques_theoriques(formulas, sequence_jusqu_n, n)

    # Calculer les métriques et différentiels pour chaque possibilité
    metriques_par_possibilite = MetriquesTheorique{Float64}[]
    differentiels_par_possibilite = DifferentielsPredictifs{Float64}[]

    for valeur_possible in valeurs_possibles
        # Créer une séquence hypothétique avec cette valeur ajoutée
        sequence_hypothetique = vcat(sequence_jusqu_n, [valeur_possible])
        n_hypothetique = length(sequence_hypothetique)

        # Calculer toutes les métriques théoriques pour cette séquence hypothétique (main n+1)
        metriques_n_plus_1 = calculer_toutes_metriques_theoriques(formulas, sequence_hypothetique, n_hypothetique)
        push!(metriques_par_possibilite, metriques_n_plus_1)

        # Calculer les différentiels |métrique(n+1) - métrique(n)|
        diff = calculer_differentiels_predictifs(calculateur_diff, metriques_n, metriques_n_plus_1)
        push!(differentiels_par_possibilite, diff)
    end

    return PredictionResult(
        main_actuelle.main_number,
        main_actuelle.index5,
        index1_suivant,
        index5_observe,
        valeurs_possibles,
        metriques_par_possibilite,
        differentiels_par_possibilite
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# AFFICHAGE ET INTERFACE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    exporter_prediction_vers_fichier(fichier::IO, prediction::PredictionResult)

Exporte une prédiction vers un fichier texte avec le format détaillé.
"""
function exporter_prediction_vers_fichier(fichier::IO, prediction::PredictionResult)
    println(fichier, "="^100)
    println(fichier, "🎯 PRÉDICTION POUR LA MAIN $(prediction.main_actuelle + 1)")
    println(fichier, "="^100)
    println(fichier, "📊 Main actuelle : $(prediction.main_actuelle)")
    println(fichier, "🎲 INDEX5 actuel : $(prediction.index5_actuel)")
    println(fichier, "🔄 INDEX1 suivant : $(prediction.index1_suivant)")

    # Afficher l'INDEX5 observé (toujours afficher la ligne)
    if prediction.index5_observe !== nothing
        println(fichier, "👁️  INDEX5 observé : $(prediction.index5_observe)")
    else
        println(fichier, "👁️  INDEX5 observé : (non disponible)")
    end

    println(fichier, "\n📋 Les 6 valeurs possibles avec leurs métriques théoriques et différentiels :")

    # En-tête du tableau avec métriques et différentiels selon l'ordre de priorité prédictive (InfoMutT et CondDecT supprimés)
    # Chaque bloc a une largeur fixe de 16 caractères pour assurer l'alignement parfait
    header = @sprintf("%-15s %-16s %-16s %-16s %-16s %-16s %-16s %-16s %-16s",
        "INDEX5", "CondT |Diff", "DivKLT |Diff", "CrossT |Diff", "MetricT |Diff",
        "TopoT |Diff", "TauxT |Diff", "ShannonT|Diff", "BlockT |Diff")
    println(fichier, header)
    println(fichier, "-"^length(header))

    for (i, (valeur, metriques, differentiels)) in enumerate(zip(prediction.valeurs_possibles, prediction.metriques_par_possibilite, prediction.differentiels_par_possibilite))
        # Formater chaque bloc individuellement selon l'ordre de priorité prédictive (InfoMutT et CondDecT supprimés)
        bloc1 = @sprintf("%-6.4f|%-8.4f", metriques.cond_t, differentiels.diff_cond_t)          # 1. CondT
        bloc2 = @sprintf("%-7.4f|%-8.4f", metriques.divkl_t, differentiels.diff_divkl_t)        # 2. DivKLT
        bloc3 = @sprintf("%-7.4f|%-8.4f", metriques.cross_t, differentiels.diff_cross_t)        # 3. CrossT
        bloc4 = @sprintf("%-8.4f|%-7.4f", metriques.metric_t, differentiels.diff_metric_t)      # 4. MetricT
        bloc5 = @sprintf("%-6.4f|%-8.4f", metriques.topo_t, differentiels.diff_topo_t)          # 5. TopoT
        bloc6 = @sprintf("%-6.4f|%-8.4f", metriques.taux_t, differentiels.diff_taux_t)          # 6. TauxT
        bloc7 = @sprintf("%-8.4f|%-7.4f", metriques.shannon_t, differentiels.diff_shannon_t)    # 7. ShannonT
        bloc8 = @sprintf("%-7.4f|%-8.4f", metriques.block_t, differentiels.diff_block_t)        # 8. BlockT

        ligne = @sprintf("%-15s %-16s %-16s %-16s %-16s %-16s %-16s %-16s %-16s",
            valeur, bloc1, bloc2, bloc3, bloc4, bloc5, bloc6, bloc7, bloc8)
        println(fichier, ligne)
    end

    println(fichier, "-"^length(header))
    println(fichier, "✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles avec métriques et différentiels calculés")
    println(fichier)
end

"""
    afficher_prediction(prediction::PredictionResult, compact::Bool=false, avec_metriques::Bool=false)

Affiche le résultat de prédiction de manière formatée.
"""
function afficher_prediction(prediction::PredictionResult, compact::Bool=false, avec_metriques::Bool=false)
    if compact && !avec_metriques
        # Affichage compact pour les listes longues
        println("\n📊 Main $(prediction.main_actuelle) → Main $(prediction.main_actuelle + 1)")
        println("   INDEX5 actuel: $(prediction.index5_actuel) | INDEX1 suivant: $(prediction.index1_suivant)")
        print("   Valeurs possibles: ")
        println(join(prediction.valeurs_possibles, " "))
    elseif avec_metriques
        # Affichage avec métriques théoriques
        println("\n" * "="^100)
        println("🎯 PRÉDICTION AVEC MÉTRIQUES POUR LA MAIN $(prediction.main_actuelle + 1)")
        println("="^100)
        println("📊 Main actuelle : $(prediction.main_actuelle)")
        println("🎲 INDEX5 actuel : $(prediction.index5_actuel)")
        println("🔄 INDEX1 suivant : $(prediction.index1_suivant)")

        # Afficher l'INDEX5 observé (toujours afficher la ligne)
        if prediction.index5_observe !== nothing
            println("👁️  INDEX5 observé : $(prediction.index5_observe)")
        else
            println("👁️  INDEX5 observé : (non disponible)")
        end

        println("\n📋 Les 6 valeurs possibles avec leurs métriques théoriques et différentiels :")
        println("-"^200)

        # En-tête du tableau avec métriques et différentiels selon l'ordre de priorité prédictive (InfoMutT et CondDecT supprimés)
        println(@sprintf("%-15s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s",
            "INDEX5", "CondT", "Diff", "DivKLT", "Diff", "CrossT", "Diff", "MetricT", "Diff",
            "TopoT", "Diff", "TauxT", "Diff", "ShannonT", "Diff", "BlockT", "Diff"))
        println("-"^160)

        for (i, (valeur, metriques, differentiels)) in enumerate(zip(prediction.valeurs_possibles, prediction.metriques_par_possibilite, prediction.differentiels_par_possibilite))
            println(@sprintf("%-15s %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f",
                valeur,
                metriques.cond_t, differentiels.diff_cond_t,            # 1. CondT - PRIORITÉ 1
                metriques.divkl_t, differentiels.diff_divkl_t,          # 2. DivKLT - PRIORITÉ 2
                metriques.cross_t, differentiels.diff_cross_t,          # 3. CrossT - PRIORITÉ 2
                metriques.metric_t, differentiels.diff_metric_t,        # 4. MetricT - PRIORITÉ 3
                metriques.topo_t, differentiels.diff_topo_t,            # 5. TopoT - PRIORITÉ 3
                metriques.taux_t, differentiels.diff_taux_t,            # 6. TauxT - PRIORITÉ 4
                metriques.shannon_t, differentiels.diff_shannon_t,      # 7. ShannonT - PRIORITÉ 5
                metriques.block_t, differentiels.diff_block_t))         # 8. BlockT - PRIORITÉ 5
        end

        println("-"^200)
        println("✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles avec métriques et différentiels calculés")
    else
        # Affichage détaillé standard
        println("\n" * "="^80)
        println("🎯 PRÉDICTION POUR LA MAIN $(prediction.main_actuelle + 1)")
        println("="^80)
        println("📊 Main actuelle : $(prediction.main_actuelle)")
        println("🎲 INDEX5 actuel : $(prediction.index5_actuel)")
        println("🔄 INDEX1 suivant : $(prediction.index1_suivant)")
        println("\n📋 Les 6 valeurs possibles d'INDEX5 pour la main $(prediction.main_actuelle + 1) :")
        println("-"^50)

        for (i, valeur) in enumerate(prediction.valeurs_possibles)
            println(@sprintf("%2d. %s", i, valeur))
        end

        println("-"^50)
        println("✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles")
    end
end

"""
    afficher_regles()

Affiche les règles de transition d'INDEX1.
"""
function afficher_regles()
    println("\n📜 RÈGLES DE TRANSITION INDEX1 :")
    println("="^50)
    println("• Si INDEX2 = C → INDEX1 s'inverse (0→1, 1→0)")
    println("• Si INDEX2 = A → INDEX1 reste identique")
    println("• Si INDEX2 = B → INDEX1 reste identique")
    println("="^50)
end

# ═══════════════════════════════════════════════════════════════════════════════
# PROGRAMME PRINCIPAL
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Fonction principale du programme.
"""
function main()
    println("🚀 PRÉDICTEUR INDEX5 - DÉMARRAGE")
    println("="^60)
    
    try
        # 1. Charger automatiquement le fichier JSON le plus récent
        dossier_partie = "partie"
        chemin_fichier = trouver_fichier_json_recent(dossier_partie)

        # 2. Compter les parties disponibles
        nb_parties = compter_parties_disponibles(chemin_fichier)
        println("📊 Nombre de parties disponibles : $nb_parties")

        if nb_parties == 0
            println("❌ Aucune partie trouvée dans le fichier")
            return
        end

        # 3. Demander à l'utilisateur de choisir une partie
        println("\n🎯 SÉLECTION DE LA PARTIE")
        println("="^50)

        numero_partie = 0
        while true
            print("➤ Choisissez le numéro de partie (1-$nb_parties) : ")
            input = strip(readline())

            try
                numero_partie = parse(Int, input)
                if numero_partie >= 1 && numero_partie <= nb_parties
                    break
                else
                    println("❌ Numéro invalide. Doit être entre 1 et $nb_parties")
                end
            catch
                println("❌ Veuillez entrer un numéro valide")
            end
        end

        # 4. Charger la partie sélectionnée
        mains = charger_donnees_partie(chemin_fichier, numero_partie)

        if isempty(mains)
            println("❌ Aucune main valide trouvée dans la partie $numero_partie")
            return
        end

        # 5. Afficher les règles
        afficher_regles()

        # 6. Demander le mode d'affichage
        println("\n🎯 MODE D'AFFICHAGE")
        println("="^50)
        println("1. Affichage avec métriques théoriques et export automatique")

        mode_affichage = 1
        while true
            print("➤ Appuyez sur Entrée pour continuer avec l'export automatique : ")
            input = strip(readline())

            # Accepter n'importe quelle entrée (y compris vide) pour continuer
            mode_affichage = 1
            break
        end

        avec_metriques = true  # Toujours avec métriques maintenant

        # 7. Générer les prédictions pour toutes les mains 1 à 59
        println("\n🎯 GÉNÉRATION DES PRÉDICTIONS POUR LA PARTIE $numero_partie (mains 1 à 59)")
        println("="^80)

        # Calculer le nombre maximum de mains à traiter (59 ou moins si le fichier en contient moins)
        max_mains = min(59, length(mains))

        println("📊 Traitement de $max_mains mains...")
        if avec_metriques
            println("⚡ Calcul des 10 métriques théoriques pour chaque possibilité...")
        end

        # Construire la séquence INDEX5 pour les calculs d'entropie
        sequence_index5 = [main.index5 for main in mains]

        # Générer toutes les prédictions
        predictions = PredictionResult[]

        for i in 1:max_mains
            main_actuelle = mains[i]
            # Utiliser la séquence jusqu'à la main actuelle pour les calculs d'entropie
            sequence_jusqu_n = sequence_index5[1:i]

            # Récupérer l'INDEX5 observé à la main n+1 (si disponible)
            index5_observe = nothing
            if i < length(mains)  # S'il y a une main suivante
                index5_observe = mains[i + 1].index5
            end

            # Toujours calculer avec métriques et différentiels
            prediction = predire_main_suivante(main_actuelle, sequence_jusqu_n, index5_observe)

            push!(predictions, prediction)
        end

        # Exporter automatiquement vers un fichier texte
        nom_fichier_export = "predictions_partie_$(numero_partie)_$(Dates.format(now(), "yyyymmdd_HHMMSS")).txt"

        println("📁 Export automatique vers : $nom_fichier_export")

        open(nom_fichier_export, "w") do fichier
            # En-tête du fichier
            println(fichier, "PRÉDICTIONS INDEX5 - PARTIE $numero_partie")
            println(fichier, "="^80)
            println(fichier, "Généré le : $(Dates.format(now(), "dd/mm/yyyy à HH:MM:SS"))")
            println(fichier, "Nombre de prédictions : $(length(predictions))")
            println(fichier, "Métriques calculées : 10 métriques théoriques avec différentiels")
            println(fichier, "="^80)
            println(fichier)

            # Exporter chaque prédiction
            for prediction in predictions
                exporter_prediction_vers_fichier(fichier, prediction)
            end

            # Pied de page
            println(fichier)
            println(fichier, "="^80)
            println(fichier, "FIN DU RAPPORT - $(length(predictions)) prédictions exportées")
            println(fichier, "="^80)
        end

        # Résumé final
        println("\n" * "="^80)
        println("✅ RÉSUMÉ FINAL - PARTIE $numero_partie")
        println("="^80)
        println("📈 Nombre total de prédictions générées : $(length(predictions))")
        println("🎲 Chaque prédiction contient 6 valeurs possibles d'INDEX5 (TIE exclu)")
        println("📊 10 métriques théoriques avec différentiels calculées pour chaque possibilité")
        println("📋 Total de calculs de métriques : $(length(predictions) * 6 * 10)")
        println("📋 Total de calculs de différentiels : $(length(predictions) * 6 * 10)")
        println("📁 Résultats exportés automatiquement vers : $nom_fichier_export")
        println("="^80)
        
    catch e
        println("💥 Erreur fatale : $e")
        return 1
    end
    
    return 0
end

# ============================================================================
# NOUVELLE CLASSE POUR PRÉDICTIONS BASÉES SUR LES DIFFÉRENTIELS (SANS InfoMutT)
# ============================================================================

struct PredicteurDifferentiels
    nom_fichier_resultats::String
    predictions_correctes::Ref{Int}
    predictions_incorrectes::Ref{Int}
    predictions_ties::Ref{Int}
    correctes_consecutives::Ref{Int}
    incorrectes_consecutives::Ref{Int}
    max_correctes_consecutives::Ref{Int}
    max_incorrectes_consecutives::Ref{Int}

    function PredicteurDifferentiels(nom_fichier::String)
        new(nom_fichier, Ref(0), Ref(0), Ref(0), Ref(0), Ref(0), Ref(0), Ref(0))
    end
end

function calculer_somme_differentiels_groupe(resultats_simulation, groupe::String)
    """Calcule la somme des 9 différentiels (sans InfoMutT) pour un groupe BANKER ou PLAYER"""
    somme_totale = 0.0

    for (index5, metriques) in resultats_simulation
        if occursin(groupe, index5)
            # Additionner les 8 différentiels (InfoMutT et CondDecT exclus)
            somme_totale += metriques["Diff_CondT"]
            somme_totale += metriques["Diff_DivKLT"]
            somme_totale += metriques["Diff_CrossT"]
            somme_totale += metriques["Diff_MetricT"]
            somme_totale += metriques["Diff_TopoT"]
            somme_totale += metriques["Diff_TauxT"]
            somme_totale += metriques["Diff_ShannonT"]
            somme_totale += metriques["Diff_BlockT"]
        end
    end

    return somme_totale
end

function predire_index3(predicteur::PredicteurDifferentiels, resultats_simulation)
    """Prédit INDEX3 basé sur la somme des différentiels la plus élevée"""

    somme_banker = calculer_somme_differentiels_groupe(resultats_simulation, "BANKER")
    somme_player = calculer_somme_differentiels_groupe(resultats_simulation, "PLAYER")

    if somme_banker > somme_player
        return "BANKER", somme_banker, somme_player
    else
        return "PLAYER", somme_banker, somme_player
    end
end

function mettre_a_jour_compteurs(predicteur::PredicteurDifferentiels, prediction_correcte::Bool)
    """Met à jour les compteurs de prédictions consécutives"""

    if prediction_correcte
        predicteur.predictions_correctes[] += 1
        predicteur.correctes_consecutives[] += 1
        predicteur.incorrectes_consecutives[] = 0

        if predicteur.correctes_consecutives[] > predicteur.max_correctes_consecutives[]
            predicteur.max_correctes_consecutives[] = predicteur.correctes_consecutives[]
        end
    else
        predicteur.predictions_incorrectes[] += 1
        predicteur.incorrectes_consecutives[] += 1
        predicteur.correctes_consecutives[] = 0

        if predicteur.incorrectes_consecutives[] > predicteur.max_incorrectes_consecutives[]
            predicteur.max_incorrectes_consecutives[] = predicteur.incorrectes_consecutives[]
        end
    end
end

function extraire_index3(index5::String)
    """Extrait INDEX3 de INDEX5 (format: INDEX1_INDEX2_INDEX3)"""
    parties = split(index5, "_")
    if length(parties) >= 3
        return parties[3]
    else
        return "UNKNOWN"
    end
end

# Lancer le programme si exécuté directement
if abspath(PROGRAM_FILE) == @__FILE__
    exit(main())
end
