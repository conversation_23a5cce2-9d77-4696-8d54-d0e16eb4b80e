FORMULES COMPLÈTES DES 8 MÉTRIQUES D'ENTROPIE - PREDICTEUR INDEX5
=======================================================================

Ce document détaille les formules mathématiques complètes pour chacune des 8 métriques
d'entropie utilisées dans le système de prédiction INDEX5.

CLASSIFICATION DES MÉTRIQUES :
- EMPIRIQUES (2) : BlockT, TauxT - basées sur les observations réelles
- THÉORIQUES (6) : CondT, MetricT, DivKLT, CrossT, TopoT, ShannonT - basées sur le modèle INDEX5

═══════════════════════════════════════════════════════════════════════════════════════
1. BlockT - ENTROPIE DE BLOC EMPIRIQUE PURE
═══════════════════════════════════════════════════════════════════════════════════════

FORMULE GÉNÉRALE :
BlockT_n = H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)

FORMULE DÉTAILLÉE :
BlockT_n = -log₂(1/18) + ∑ᵢ₌₂ⁿ (-log₂(p_emp(xᵢ|xᵢ₋₁)))

CALCUL ÉTAPE PAR ÉTAPE :

1. PREMIER TERME (i = 1) :
   H(X₁) = -log₂(1/18) ≈ 4.170 bits
   
   Explication : Probabilité empirique uniforme sur 18 valeurs INDEX5 possibles
   18 valeurs = 2 × 3 × 3 = INDEX1(0,1) × INDEX2(A,B,C) × INDEX3(BANKER,PLAYER,TIE)

2. TERMES SUIVANTS (i = 2, 3, ..., n) :
   H(Xᵢ|Xᵢ₋₁) = -log₂(p_emp(xᵢ|xᵢ₋₁))
   
   Où : p_emp(xᵢ|xᵢ₋₁) = count(xᵢ₋₁ → xᵢ) / count(xᵢ₋₁)
   
   count(xᵢ₋₁ → xᵢ) = nombre de transitions de xᵢ₋₁ vers xᵢ dans la séquence complète
   count(xᵢ₋₁) = nombre total d'occurrences de xᵢ₋₁ dans la séquence complète

EXEMPLE POUR UNE SÉQUENCE ["0_A_BANKER", "1_B_PLAYER", "0_C_BANKER"] :

BlockT₃ = -log₂(1/18) + (-log₂(p_emp("1_B_PLAYER"|"0_A_BANKER"))) + (-log₂(p_emp("0_C_BANKER"|"1_B_PLAYER")))

Si dans la séquence complète :
- "0_A_BANKER" → "1_B_PLAYER" apparaît 3 fois sur 10 occurrences de "0_A_BANKER"
- "1_B_PLAYER" → "0_C_BANKER" apparaît 2 fois sur 8 occurrences de "1_B_PLAYER"

Alors :
BlockT₃ = 4.170 + (-log₂(3/10)) + (-log₂(2/8))
        = 4.170 + 1.737 + 2.000
        = 7.907 bits

═══════════════════════════════════════════════════════════════════════════════════════
2. TauxT - TAUX D'ENTROPIE EMPIRIQUE
═══════════════════════════════════════════════════════════════════════════════════════

FORMULE GÉNÉRALE :
TauxT_n = BlockT_n / n

FORMULE DÉTAILLÉE :
TauxT_n = [-log₂(1/18) + ∑ᵢ₌₂ⁿ (-log₂(p_emp(xᵢ|xᵢ₋₁)))] / n

CALCUL ÉTAPE PAR ÉTAPE :

1. Calculer BlockT_n selon la formule ci-dessus
2. Diviser par n (longueur de la séquence)

EXEMPLE (suite de l'exemple BlockT) :
TauxT₃ = BlockT₃ / 3 = 7.907 / 3 = 2.636 bits/élément

SIGNIFICATION :
Complexité informationnelle moyenne par élément de la séquence

═══════════════════════════════════════════════════════════════════════════════════════
3. CondT - ENTROPIE CONDITIONNELLE THÉORIQUE
═══════════════════════════════════════════════════════════════════════════════════════

FORMULE GÉNÉRALE :
CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_INDEX5(Xᵢ|X₁,...,Xᵢ₋₁)

FORMULE DÉTAILLÉE :
CondT_n = (1/n) × [H_INDEX5(X₁) + ∑ᵢ₌₂ⁿ (H_shannon_INDEX5(X₁,...,Xᵢ) - H_shannon_INDEX5(X₁,...,Xᵢ₋₁))]

CALCUL ÉTAPE PAR ÉTAPE :

1. PREMIER TERME :
   H_INDEX5(X₁) = -log₂(p_INDEX5(x₁))
   
   Où p_INDEX5(x₁) est la probabilité théorique INDEX5 du premier élément

2. TERMES SUIVANTS :
   H_INDEX5(Xᵢ|X₁,...,Xᵢ₋₁) = H_shannon_INDEX5(X₁,...,Xᵢ) - H_shannon_INDEX5(X₁,...,Xᵢ₋₁)

3. CALCUL DE H_shannon_INDEX5(X₁,...,Xₖ) :
   H_shannon_INDEX5(X₁,...,Xₖ) = ∑_{valeurs uniques observées dans [1:k]} p_INDEX5(valeur) × (-log₂(p_INDEX5(valeur)))
   
   Note importante : Utilise les probabilités INDEX5, pas les fréquences observées

4. MOYENNE :
   CondT_n = (somme de tous les termes) / n

EXEMPLE POUR ["0_A_BANKER", "1_B_PLAYER", "0_C_BANKER"] :

1. H_INDEX5(X₁) = -log₂(0.085136) ≈ 3.555 bits

2. H_shannon_INDEX5(X₁,X₂) = p_INDEX5("0_A_BANKER") × (-log₂(p_INDEX5("0_A_BANKER"))) + 
                              p_INDEX5("1_B_PLAYER") × (-log₂(p_INDEX5("1_B_PLAYER")))
                            = 0.085136 × 3.555 + 0.077888 × 3.683
                            ≈ 0.590 bits

3. H_shannon_INDEX5(X₁) = p_INDEX5("0_A_BANKER") × (-log₂(p_INDEX5("0_A_BANKER")))
                        = 0.085136 × 3.555 ≈ 0.303 bits

4. H_INDEX5(X₂|X₁) = 0.590 - 0.303 = 0.287 bits

5. CondT₃ = (3.555 + 0.287 + ...) / 3

═══════════════════════════════════════════════════════════════════════════════════════
4. MetricT - ENTROPIE MÉTRIQUE KOLMOGOROV-SINAI THÉORIQUE
═══════════════════════════════════════════════════════════════════════════════════════

FORMULE GÉNÉRALE :
MetricT_n = Complexité_pondérée(n) - Complexité_pondérée(n-1)

FORMULE DÉTAILLÉE :
MetricT_n = [(2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_INDEX5(Xᵢ|X₁,...,Xᵢ₋₁)] - 
            [(2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_INDEX5(Xᵢ|X₁,...,Xᵢ₋₁)]

CALCUL ÉTAPE PAR ÉTAPE :

1. CALCUL DES ENTROPIES CONDITIONNELLES :
   Même méthode que CondT pour calculer H_INDEX5(Xᵢ|X₁,...,Xᵢ₋₁)

2. COMPLEXITÉ PONDÉRÉE POUR n :
   Complexité_n = (2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_INDEX5(Xᵢ|X₁,...,Xᵢ₋₁)

3. COMPLEXITÉ PONDÉRÉE POUR n-1 :
   Complexité_{n-1} = (2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_INDEX5(Xᵢ|X₁,...,Xᵢ₋₁)

4. DIFFÉRENCE :
   MetricT_n = Complexité_n - Complexité_{n-1}

EXEMPLE POUR n=3 :

1. Calculer H_INDEX5(X₁), H_INDEX5(X₂|X₁), H_INDEX5(X₃|X₁,X₂) comme dans CondT

2. Complexité₃ = (2/(3×4)) × [1×H_INDEX5(X₁) + 2×H_INDEX5(X₂|X₁) + 3×H_INDEX5(X₃|X₁,X₂)]
                = (1/6) × [1×3.555 + 2×0.287 + 3×H_INDEX5(X₃|X₁,X₂)]

3. Complexité₂ = (2/(2×3)) × [1×H_INDEX5(X₁) + 2×H_INDEX5(X₂|X₁)]
                = (1/3) × [1×3.555 + 2×0.287]

4. MetricT₃ = Complexité₃ - Complexité₂

SIGNIFICATION :
Mesure l'impact de l'ajout du n-ème élément sur la complexité informationnelle pondérée

═══════════════════════════════════════════════════════════════════════════════════════
5. DivKLT - DIVERGENCE DE KULLBACK-LEIBLER THÉORIQUE
═══════════════════════════════════════════════════════════════════════════════════════

FORMULE GÉNÉRALE :
DivKLT_n = ∑ p_obs(x) × log₂(p_obs(x)/p_INDEX5(x))

FORMULE DÉTAILLÉE :
DivKLT_n = ∑_{x observé dans [1:n]} (count(x)/n) × log₂((count(x)/n)/p_INDEX5(x))

CALCUL ÉTAPE PAR ÉTAPE :

1. COMPTER LES OCCURRENCES :
   Pour chaque valeur unique x observée dans la séquence [1:n] :
   count(x) = nombre d'occurrences de x dans [1:n]

2. CALCULER LES PROBABILITÉS OBSERVÉES :
   p_obs(x) = count(x) / n

3. RÉCUPÉRER LES PROBABILITÉS THÉORIQUES :
   p_INDEX5(x) = probabilité théorique INDEX5 de x

4. CALCULER LA DIVERGENCE :
   DivKLT_n = ∑ p_obs(x) × log₂(p_obs(x)/p_INDEX5(x))

EXEMPLE POUR ["0_A_BANKER", "1_B_PLAYER", "0_A_BANKER"] :

1. Occurrences : "0_A_BANKER" = 2, "1_B_PLAYER" = 1

2. Probabilités observées :
   p_obs("0_A_BANKER") = 2/3 ≈ 0.667
   p_obs("1_B_PLAYER") = 1/3 ≈ 0.333

3. Probabilités INDEX5 :
   p_INDEX5("0_A_BANKER") = 0.085136
   p_INDEX5("1_B_PLAYER") = 0.077888

4. Divergence :
   DivKLT₃ = 0.667 × log₂(0.667/0.085136) + 0.333 × log₂(0.333/0.077888)
           = 0.667 × log₂(7.836) + 0.333 × log₂(4.276)
           = 0.667 × 2.970 + 0.333 × 2.095
           = 1.981 + 0.698 = 2.679 bits

SIGNIFICATION :
Mesure l'écart entre les fréquences réellement observées et les probabilités théoriques INDEX5

═══════════════════════════════════════════════════════════════════════════════════════
6. CrossT - ENTROPIE CROISÉE THÉORIQUE
═══════════════════════════════════════════════════════════════════════════════════════

FORMULE GÉNÉRALE :
CrossT_n = -∑ p_obs(x) × log₂(p_INDEX5(x))

FORMULE DÉTAILLÉE :
CrossT_n = -∑_{x observé dans [1:n]} (count(x)/n) × log₂(p_INDEX5(x))

CALCUL ÉTAPE PAR ÉTAPE :

1. COMPTER LES OCCURRENCES :
   Même méthode que DivKLT

2. CALCULER LES PROBABILITÉS OBSERVÉES :
   p_obs(x) = count(x) / n

3. RÉCUPÉRER LES PROBABILITÉS THÉORIQUES :
   p_INDEX5(x) = probabilité théorique INDEX5 de x

4. CALCULER L'ENTROPIE CROISÉE :
   CrossT_n = -∑ p_obs(x) × log₂(p_INDEX5(x))

EXEMPLE (suite de l'exemple DivKLT) :

CrossT₃ = -(0.667 × log₂(0.085136) + 0.333 × log₂(0.077888))
        = -(0.667 × (-3.555) + 0.333 × (-3.683))
        = -(-2.371 + (-1.226))
        = -(-3.597) = 3.597 bits

SIGNIFICATION :
Coût d'encodage des données réellement observées en utilisant les probabilités théoriques INDEX5

═══════════════════════════════════════════════════════════════════════════════════════
7. TopoT - ENTROPIE TOPOLOGIQUE MULTI-ÉCHELLES THÉORIQUE
═══════════════════════════════════════════════════════════════════════════════════════

FORMULE GÉNÉRALE :
TopoT_n = 0.167×H_INDEX5(blocs₁) + 0.333×H_INDEX5(blocs₂) + 0.500×H_INDEX5(blocs₃)

FORMULE DÉTAILLÉE :
TopoT_n = 0.167×H_INDEX5(blocs₁) + 0.333×H_INDEX5(blocs₂) + 0.500×H_INDEX5(blocs₃)

Où H_INDEX5(blocsₖ) = ∑_{blocs distincts de taille k} p_INDEX5(bloc) × (-log₂(p_INDEX5(bloc)))

CALCUL ÉTAPE PAR ÉTAPE :

1. DÉFINITION DES BLOCS :
   - blocs₁ = valeurs individuelles de la séquence [1:n]
   - blocs₂ = paires consécutives de la séquence [1:n] (si n≥2)
   - blocs₃ = triplets consécutifs de la séquence [1:n] (si n≥3)

2. EXTRACTION DES BLOCS DISTINCTS :
   Pour chaque taille k, extraire UNIQUEMENT les blocs distincts de taille k
   (Utilise Set{Vector{String}} pour éliminer les doublons)

3. CALCUL DES PROBABILITÉS DES BLOCS :
   p_INDEX5(bloc) = ∏_{valeurs du bloc} p_INDEX5(valeur)
   (Hypothèse d'indépendance selon le modèle INDEX5)

4. CALCUL DE L'ENTROPIE POUR CHAQUE ÉCHELLE :
   H_INDEX5(blocsₖ) = ∑_{blocs distincts de taille k} p_INDEX5(bloc) × (-log₂(p_INDEX5(bloc)))

   IMPORTANT : Seuls les blocs distincts contribuent à l'entropie

5. COMBINAISON PONDÉRÉE :
   TopoT_n = 0.167×H₁ + 0.333×H₂ + 0.500×H₃

EXEMPLE POUR ["0_A_BANKER", "1_B_PLAYER", "0_C_BANKER"] :

1. Extraction des blocs distincts :
   - blocs₁ distincts = {"0_A_BANKER", "1_B_PLAYER", "0_C_BANKER"} (3 blocs distincts)
   - blocs₂ distincts = {["0_A_BANKER","1_B_PLAYER"], ["1_B_PLAYER","0_C_BANKER"]} (2 blocs distincts)
   - blocs₃ distincts = {["0_A_BANKER","1_B_PLAYER","0_C_BANKER"]} (1 bloc distinct)

2. Probabilités des blocs distincts :
   - p_INDEX5("0_A_BANKER") = 0.085136
   - p_INDEX5("1_B_PLAYER") = 0.077888
   - p_INDEX5("0_C_BANKER") = 0.077903
   - p_INDEX5(["0_A_BANKER","1_B_PLAYER"]) = 0.085136 × 0.077888 = 0.006632
   - p_INDEX5(["1_B_PLAYER","0_C_BANKER"]) = 0.077888 × 0.077903 = 0.006067
   - p_INDEX5(["0_A_BANKER","1_B_PLAYER","0_C_BANKER"]) = 0.085136 × 0.077888 × 0.077903 = 0.000517

3. Entropies par échelle :
   - H₁ = 0.085136×(-log₂(0.085136)) + 0.077888×(-log₂(0.077888)) + 0.077903×(-log₂(0.077903))
        = 0.085136×3.555 + 0.077888×3.683 + 0.077903×3.683
        = 0.303 + 0.287 + 0.287 = 0.877 bits
   - H₂ = 0.006632×(-log₂(0.006632)) + 0.006067×(-log₂(0.006067))
        = 0.006632×7.238 + 0.006067×7.368
        = 0.048 + 0.045 = 0.093 bits
   - H₃ = 0.000517×(-log₂(0.000517)) = 0.000517×10.921 = 0.006 bits

4. TopoT₃ = 0.167×0.877 + 0.333×0.093 + 0.500×0.006
          = 0.146 + 0.031 + 0.003 = 0.180 bits

SIGNIFICATION :
Entropie multi-échelles capturant les patterns à différents niveaux de résolution temporelle

═══════════════════════════════════════════════════════════════════════════════════════
8. ShannonT - ENTROPIE DE SHANNON THÉORIQUE
═══════════════════════════════════════════════════════════════════════════════════════

FORMULE GÉNÉRALE :
ShannonT_n = ∑ p_INDEX5(x_observé) × (-log₂(p_INDEX5(x_observé)))

FORMULE DÉTAILLÉE :
ShannonT_n = ∑_{valeurs uniques observées dans [1:n]} p_INDEX5(valeur) × (-log₂(p_INDEX5(valeur)))

CALCUL ÉTAPE PAR ÉTAPE :

1. IDENTIFIER LES VALEURS UNIQUES :
   Extraire toutes les valeurs distinctes observées dans la séquence [1:n]

2. RÉCUPÉRER LES PROBABILITÉS THÉORIQUES :
   Pour chaque valeur unique, récupérer p_INDEX5(valeur)

3. CALCULER L'ENTROPIE DE SHANNON :
   ShannonT_n = ∑ p_INDEX5(valeur) × (-log₂(p_INDEX5(valeur)))

EXEMPLE POUR ["0_A_BANKER", "1_B_PLAYER", "0_A_BANKER"] :

1. Valeurs uniques : {"0_A_BANKER", "1_B_PLAYER"}

2. Probabilités INDEX5 :
   p_INDEX5("0_A_BANKER") = 0.085136
   p_INDEX5("1_B_PLAYER") = 0.077888

3. Entropie :
   ShannonT₃ = 0.085136 × (-log₂(0.085136)) + 0.077888 × (-log₂(0.077888))
             = 0.085136 × 3.555 + 0.077888 × 3.683
             = 0.303 + 0.287 = 0.590 bits

SIGNIFICATION :
Entropie de Shannon pure utilisant les probabilités théoriques INDEX5 des valeurs observées

═══════════════════════════════════════════════════════════════════════════════════════
PROBABILITÉS THÉORIQUES INDEX5 DE RÉFÉRENCE
═══════════════════════════════════════════════════════════════════════════════════════

"0_A_BANKER" => 0.085136    "1_A_BANKER" => 0.086389
"0_B_BANKER" => 0.064676    "1_B_BANKER" => 0.065479
"0_C_BANKER" => 0.077903    "1_C_BANKER" => 0.078929
"0_A_PLAYER" => 0.085240    "1_A_PLAYER" => 0.086361
"0_B_PLAYER" => 0.076907    "1_B_PLAYER" => 0.077888
"0_C_PLAYER" => 0.059617    "1_C_PLAYER" => 0.060352
"0_A_TIE" => 0.017719       "1_A_TIE" => 0.017978
"0_B_TIE" => 0.016281       "1_B_TIE" => 0.016482
"0_C_TIE" => 0.013241       "1_C_TIE" => 0.013423

═══════════════════════════════════════════════════════════════════════════════════════
RÉSUMÉ DES CARACTÉRISTIQUES
═══════════════════════════════════════════════════════════════════════════════════════

MÉTRIQUES EMPIRIQUES :
- BlockT : Entropie jointe basée sur les transitions observées
- TauxT : Complexité moyenne par élément

MÉTRIQUES THÉORIQUES :
- CondT : Prévisibilité moyenne selon le modèle INDEX5
- MetricT : Impact pondéré de l'ajout d'éléments
- DivKLT : Écart entre observations et modèle
- CrossT : Coût d'encodage avec le modèle
- TopoT : Complexité multi-échelles
- ShannonT : Diversité théorique des valeurs observées

Toutes les métriques sont exprimées en bits (logarithme base 2).
